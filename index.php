<?php
/**
 * Main Landing Page
 * Flix Salon & SPA - PHP Version
 */

require_once __DIR__ . '/config/app.php';
require_once __DIR__ . '/includes/blog_functions.php';

// Get featured services (randomly selected on each page load)
$featuredServices = $database->fetchAll(
    "SELECT * FROM services WHERE is_active = 1 ORDER BY RAND() LIMIT 6"
);

// Get featured packages
$featuredPackages = $database->fetchAll(
    "SELECT * FROM packages WHERE is_active = 1 ORDER BY created_at DESC LIMIT 3"
);

// Get active offers
$activeOffers = $database->fetchAll(
    "SELECT * FROM offers WHERE is_active = 1 AND valid_from <= NOW() AND valid_to >= NOW() ORDER BY created_at DESC LIMIT 3"
);

// Get gallery images
$galleryImages = $database->fetchAll(
    "SELECT * FROM gallery WHERE is_active = 1 ORDER BY created_at DESC LIMIT 8"
);

// Get recent blog posts
$blogPosts = $database->fetchAll(
    "SELECT * FROM blog_posts WHERE status = 'published' ORDER BY publish_date DESC LIMIT 3"
);

$pageTitle = "Advanced Medical Aesthetics";
$pageDescription = "Experience advanced medical aesthetics at Redolence Medi Aesthetics. Professional treatments with cutting-edge technology and personalized care.";

include __DIR__ . '/includes/header.php';
?>

<!-- Preload critical images for better performance -->
<link rel="preload" as="image" href="https://images.unsplash.com/photo-**********-138dadb4c035?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80" media="(min-width: 768px)">
<link rel="preload" as="image" href="https://images.unsplash.com/photo-1522337360788-8b13dee7a37e?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80" media="(min-width: 768px)">

<style>
/* Lazy loading styles */
.lazy-image {
    opacity: 0;
    transition: opacity 0.4s ease-in-out;
}

.lazy-image.loaded {
    opacity: 1;
}

.lazy-placeholder {
    background: linear-gradient(90deg, #141414 25%, #1a1a1a 50%, #141414 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

/* Loading skeleton for images */
.image-skeleton {
    background: linear-gradient(90deg, #0a0a0a 25%, #141414 50%, #0a0a0a 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
    position: relative;
}

.image-skeleton::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 40px;
    height: 40px;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%236b7280'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z'%3E%3C/path%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: center;
    opacity: 0.3;
}

/* Fade-in animation for loaded images */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: scale(0.95);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

.fade-in-loaded {
    animation: fadeIn 0.5s ease-out;
}

/* Back to Top Button Styles */
.back-to-top {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    z-index: 1000;
    width: 3.5rem;
    height: 3.5rem;
    background: linear-gradient(135deg, #49a75c, #3d8b4e);
    border: 2px solid rgba(73, 167, 92, 0.3);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 10px 25px rgba(73, 167, 92, 0.3);
    backdrop-filter: blur(10px);
}

.back-to-top.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.back-to-top:hover {
    background: linear-gradient(135deg, #5bb36a, #49a75c);
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 15px 35px rgba(73, 167, 92, 0.4);
    border-color: rgba(73, 167, 92, 0.6);
}

.back-to-top:active {
    transform: translateY(0) scale(0.95);
}

.back-to-top svg {
    width: 1.25rem;
    height: 1.25rem;
    color: #000000;
    transition: transform 0.2s ease;
}

.back-to-top:hover svg {
    transform: translateY(-1px);
}

/* Header Auto-Hide Styles - REMOVED to keep header always visible */

/* Responsive adjustments for back to top button */
@media (max-width: 768px) {
    .back-to-top {
        bottom: 1.5rem;
        right: 1.5rem;
        width: 3rem;
        height: 3rem;
    }

    .back-to-top svg {
        width: 1rem;
        height: 1rem;
    }

    /* Hide navigation arrows on mobile - use swipe gestures instead */
    .hero-prev, .hero-next {
        display: none;
    }

    /* Show dots indicator more prominently on mobile */
    .hero-carousel .absolute.bottom-8 {
        bottom: 1.5rem;
    }

    .hero-dot {
        width: 0.75rem !important;
        height: 0.75rem !important;
    }
}

/* Hero Section Parallax Styles */
.hero-section {
    position: relative;
    overflow: hidden;
}

.parallax-bg {
    position: absolute;
    top: -20%;
    left: 0;
    width: 100%;
    height: 120%;
    will-change: transform;
    z-index: 1;
}

.parallax-element {
    will-change: transform;
    transition: transform 0.1s ease-out;
}

/* Enhanced Card Hover Effects */
.hero-main-card, .hero-side-card {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    will-change: transform;
    cursor: pointer;
}

.hero-main-card:hover, .hero-side-card:hover {
    transform: translateY(-10px) scale(1.05);
    box-shadow: 0 25px 50px rgba(0,0,0,0.15);
}

/* Smooth animations for interactive elements */
.hero-main-card img, .hero-side-card img {
    transition: transform 0.6s ease;
}

.hero-main-card:hover img, .hero-side-card:hover img {
    transform: scale(1.1);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .hero-main-card {
        margin-bottom: 2rem;
    }

    .hero-side-card {
        max-width: 300px;
        margin: 0 auto 1.5rem;
    }
}

@media (max-width: 768px) {
    /* Disable parallax on mobile for better performance */
    .parallax-element, .parallax-bg {
        transform: none !important;
    }

    .hero-section {
        min-height: auto;
        padding: 2rem 0;
    }

    .hero-main-card, .hero-side-card {
        transform: none !important;
        box-shadow: 0 10px 25px rgba(0,0,0,0.1) !important;
    }
}

/* Custom scrollbar for webkit browsers */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #49a75c;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #3d8b4e;
}
</style>

<!-- Hero Section - Reference Design Match -->
<section class="hero-section relative min-h-screen bg-gradient-to-br from-gray-50 to-white overflow-hidden">
  <!-- Parallax Background with Subtle Pattern -->
  <div class="absolute inset-0 parallax-bg" data-speed="0.5">
    <div class="absolute inset-0 bg-gradient-to-br from-redolence-green/5 to-redolence-blue/5"></div>
    <div class="absolute inset-0 opacity-30" style="background-image: radial-gradient(circle at 1px 1px, rgba(73,167,92,0.15) 1px, transparent 0); background-size: 20px 20px;"></div>
  </div>

  <!-- Floating Decorative Elements -->
  <div class="absolute top-20 right-20 w-32 h-32 bg-redolence-green/10 rounded-full blur-3xl animate-pulse"></div>
  <div class="absolute bottom-20 left-20 w-24 h-24 bg-redolence-blue/10 rounded-full blur-2xl animate-pulse" style="animation-delay: 2s;"></div>

  <div class="relative z-10 max-w-7xl mx-auto px-6 py-20">
    <div class="grid grid-cols-12 gap-8 items-center min-h-[80vh]">

      <!-- Left Content - "Heal. Enhance. Transform." -->
      <div class="col-span-12 lg:col-span-4 space-y-8">
        <div class="space-y-4">
          <h1 class="text-5xl md:text-6xl lg:text-7xl font-bold text-gray-900 leading-tight">
            <div class="block">Heal.</div>
            <div class="block">Enhance.</div>
            <div class="block">
              <span class="bg-gradient-to-r from-redolence-green to-redolence-blue bg-clip-text text-transparent">
                Transform.
              </span>
            </div>
          </h1>
        </div>

        <!-- Search/Action Section -->
        <div class="bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-gray-100/50">
          <div class="flex gap-3">
            <div class="relative flex-1">
              <input
                type="text"
                id="treatment-search"
                placeholder="Find your passion"
                class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-redolence-green/20 focus:border-redolence-green transition-all"
              >
            </div>
            <button id="search-btn" class="bg-redolence-green hover:bg-green-600 text-white px-8 py-3 rounded-xl font-semibold transition-all hover:scale-105 shadow-lg">
              Go
            </button>
          </div>
        </div>
      </div>

      <!-- Right Content - Interactive Hover Cards Layout -->
      <div class="col-span-12 lg:col-span-8">
        <div class="flex gap-4 h-96" id="hero-cards-container">

          <!-- Card 1 - Medical Aesthetics -->
          <div class="hero-hover-card bg-white rounded-3xl shadow-xl overflow-hidden border border-gray-100 relative transition-all duration-500 ease-out cursor-pointer" data-card="1">
            <div class="relative h-full overflow-hidden">
              <img
                src="https://images.unsplash.com/photo-**********-5c350d0d3c56?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80"
                alt="Advanced Medical Aesthetics"
                class="w-full h-full object-cover transition-transform duration-700 ease-out"
              >
              <div class="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"></div>

              <!-- Card Content Overlay -->
              <div class="absolute bottom-6 left-6 text-white card-content transition-all duration-500">
                <h3 class="card-title text-xl font-bold mb-2 transition-all duration-500">Medical Aesthetics</h3>
                <h4 class="card-subtitle text-sm mb-2 transition-all duration-500">Advanced Course</h4>
                <div class="card-number text-2xl font-bold transition-all duration-500">100</div>
              </div>
            </div>
          </div>

          <!-- Card 2 - Injectable Treatments -->
          <div class="hero-hover-card bg-white rounded-3xl shadow-xl overflow-hidden border border-gray-100 relative transition-all duration-500 ease-out cursor-pointer" data-card="2">
            <div class="relative h-full overflow-hidden">
              <img
                src="https://images.unsplash.com/photo-**********-4ab6ce6db874?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80"
                alt="Injectable Treatments"
                class="w-full h-full object-cover transition-transform duration-700 ease-out"
              >
              <div class="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"></div>

              <!-- Card Content Overlay -->
              <div class="absolute bottom-6 left-6 text-white card-content transition-all duration-500">
                <h3 class="card-title text-xl font-bold mb-2 transition-all duration-500">Injectable</h3>
                <h4 class="card-subtitle text-sm mb-2 transition-all duration-500">Botox & Fillers</h4>
                <div class="card-number text-2xl font-bold transition-all duration-500">50+</div>
              </div>
            </div>
          </div>

          <!-- Card 3 - Wellness Treatments -->
          <div class="hero-hover-card bg-white rounded-3xl shadow-xl overflow-hidden border border-gray-100 relative transition-all duration-500 ease-out cursor-pointer" data-card="3">
            <div class="relative h-full overflow-hidden">
              <img
                src="https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80"
                alt="Wellness Treatments"
                class="w-full h-full object-cover transition-transform duration-700 ease-out"
              >
              <div class="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"></div>

              <!-- Card Content Overlay -->
              <div class="absolute bottom-6 left-6 text-white card-content transition-all duration-500">
                <h3 class="card-title text-xl font-bold mb-2 transition-all duration-500">Wellness</h3>
                <h4 class="card-subtitle text-sm mb-2 transition-all duration-500">Holistic Care</h4>
                <div class="card-number text-2xl font-bold transition-all duration-500">30+</div>
              </div>
            </div>
          </div>

        </div>
      </div>

    </div>
  </div>

  <!-- Enhanced Parallax & Animation Script -->
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // Check if device supports smooth scrolling and isn't mobile
      const isMobile = window.innerWidth <= 768;
      const supportsParallax = !isMobile && 'requestAnimationFrame' in window;

      if (supportsParallax) {
        // Smooth parallax effect with performance optimization
        let scrollY = 0;
        let ticking = false;

        function updateScrollY() {
          scrollY = window.pageYOffset;
        }

        function parallaxEffect() {
          const parallaxElements = document.querySelectorAll('.parallax-element');
          const parallaxBg = document.querySelector('.parallax-bg');

          // Background parallax with bounds checking
          if (parallaxBg && scrollY < window.innerHeight * 2) {
            const speed = parseFloat(parallaxBg.dataset.speed) || 0.5;
            const yPos = scrollY * speed;
            parallaxBg.style.transform = `translate3d(0, ${yPos}px, 0)`;
          }

          // Element parallax with intersection observer optimization
          parallaxElements.forEach(element => {
            const rect = element.getBoundingClientRect();
            const isVisible = rect.bottom >= 0 && rect.top <= window.innerHeight;

            if (isVisible) {
              const speed = parseFloat(element.dataset.speed) || 0.3;
              const yPos = -(scrollY * speed);
              element.style.transform = `translate3d(0, ${yPos}px, 0)`;
            }
          });

          ticking = false;
        }

        function requestParallaxUpdate() {
          if (!ticking) {
            requestAnimationFrame(parallaxEffect);
            ticking = true;
          }
        }

        // Optimized scroll listener
        window.addEventListener('scroll', () => {
          updateScrollY();
          requestParallaxUpdate();
        }, { passive: true });

        // Initial call
        updateScrollY();
        parallaxEffect();
      }

      // Enhanced dynamic hover card system
      const hoverCards = document.querySelectorAll('.hero-hover-card');
      const cardsContainer = document.getElementById('hero-cards-container');

      hoverCards.forEach((card, index) => {
        // Enhanced hover effects with staggered animations
        card.addEventListener('mouseenter', function(e) {
          // Add subtle bounce effect to other cards
          hoverCards.forEach((otherCard, otherIndex) => {
            if (otherIndex !== index) {
              setTimeout(() => {
                otherCard.style.transform = 'translateY(5px) scale(0.95)';
              }, otherIndex * 50);
            }
          });

          // Add glow effect to hovered card
          this.style.boxShadow = '0 30px 60px rgba(73, 167, 92, 0.2), 0 0 0 1px rgba(73, 167, 92, 0.1)';
        });

        card.addEventListener('mouseleave', function(e) {
          // Reset all cards smoothly
          hoverCards.forEach((otherCard, otherIndex) => {
            setTimeout(() => {
              otherCard.style.transform = '';
              otherCard.style.boxShadow = '';
            }, otherIndex * 30);
          });
        });

        // Add click ripple effect with card-specific colors
        card.addEventListener('click', function(e) {
          const colors = [
            'rgba(73, 167, 92, 0.3)',   // Green for medical
            'rgba(59, 130, 246, 0.3)',  // Blue for injectable
            'rgba(168, 85, 247, 0.3)'   // Purple for wellness
          ];

          const ripple = document.createElement('div');
          const rect = this.getBoundingClientRect();
          const size = Math.max(rect.width, rect.height);
          const x = e.clientX - rect.left - size / 2;
          const y = e.clientY - rect.top - size / 2;

          ripple.style.cssText = `
            position: absolute;
            width: ${size}px;
            height: ${size}px;
            left: ${x}px;
            top: ${y}px;
            background: ${colors[index] || colors[0]};
            border-radius: 50%;
            transform: scale(0);
            animation: ripple 0.8s ease-out;
            pointer-events: none;
            z-index: 1;
          `;

          this.style.position = 'relative';
          this.appendChild(ripple);

          setTimeout(() => {
            ripple.remove();
          }, 800);

          // Navigate to relevant service page
          const servicePages = [
            '<?= getBasePath() ?>/services?category=medical-aesthetics',
            '<?= getBasePath() ?>/services?category=injectables',
            '<?= getBasePath() ?>/services?category=wellness'
          ];

          setTimeout(() => {
            window.location.href = servicePages[index] || servicePages[0];
          }, 200);
        });

        // Add subtle floating animation on idle
        setTimeout(() => {
          card.style.animation = `float${index + 1} 6s ease-in-out infinite`;
        }, index * 200);
      });

      // Enhanced search functionality
      const searchInput = document.getElementById('treatment-search');
      const searchButton = document.getElementById('search-btn');

      if (searchInput && searchButton) {
        // Search button click handler
        searchButton.addEventListener('click', function(e) {
          e.preventDefault();
          const query = searchInput.value.trim();
          if (query) {
            // Add loading state
            this.innerHTML = '<span class="relative z-10">Searching...</span>';
            this.disabled = true;

            // Simulate search delay for better UX
            setTimeout(() => {
              window.location.href = `<?= getBasePath() ?>/services?search=${encodeURIComponent(query)}`;
            }, 300);
          } else {
            // Shake animation for empty search
            searchInput.style.animation = 'shake 0.5s ease-in-out';
            setTimeout(() => {
              searchInput.style.animation = '';
            }, 500);
          }
        });

        // Enter key handler
        searchInput.addEventListener('keypress', function(e) {
          if (e.key === 'Enter') {
            searchButton.click();
          }
        });

        // Auto-complete suggestions (basic implementation)
        const suggestions = ['Botox', 'Dermal Fillers', 'Laser Hair Removal', 'Chemical Peel', 'Microneedling', 'HydraFacial', 'CoolSculpting'];

        searchInput.addEventListener('input', function(e) {
          const value = e.target.value.toLowerCase();
          // Simple auto-complete logic could be added here
          if (value.length > 2) {
            const matches = suggestions.filter(s => s.toLowerCase().includes(value));
            // Could show dropdown with matches
          }
        });
      }
    });

    // CSS for ripple animation
    const style = document.createElement('style');
    style.textContent = `
      @keyframes ripple {
        to {
          transform: scale(2);
          opacity: 0;
        }
      }
    `;
    document.head.appendChild(style);
  </script>

  <style>
    /* Dynamic Hover Cards System */
    #hero-cards-container {
      position: relative;
    }

    .hero-hover-card {
      flex: 1;
      min-width: 200px;
      transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
      will-change: transform, flex;
      position: relative;
      overflow: hidden;
      z-index: 1;
    }

    /* Default state - all cards equal size */
    .hero-hover-card {
      flex: 1;
      transform: translateY(0) scale(1);
      box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    }

    /* Hover state - expand the hovered card */
    .hero-hover-card:hover {
      flex: 2.5;
      transform: translateY(-12px) scale(1.02);
      box-shadow: 0 30px 60px rgba(0,0,0,0.25);
      z-index: 10;
    }

    /* Shrink non-hovered cards when one is hovered */
    #hero-cards-container:hover .hero-hover-card:not(:hover) {
      flex: 0.8;
      transform: translateY(0) scale(0.95);
      opacity: 0.7;
      box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }

    /* Image scaling effects */
    .hero-hover-card img {
      transition: transform 0.7s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .hero-hover-card:hover img {
      transform: scale(1.1);
    }

    #hero-cards-container:hover .hero-hover-card:not(:hover) img {
      transform: scale(1.05);
    }

    /* Text content scaling */
    .card-content {
      transform: scale(1);
      opacity: 1;
    }

    .hero-hover-card:hover .card-title {
      font-size: 1.75rem;
      margin-bottom: 0.75rem;
    }

    .hero-hover-card:hover .card-subtitle {
      font-size: 1rem;
      margin-bottom: 0.75rem;
    }

    .hero-hover-card:hover .card-number {
      font-size: 3rem;
    }

    #hero-cards-container:hover .hero-hover-card:not(:hover) .card-content {
      transform: scale(0.9);
      opacity: 0.8;
    }

    /* Parallax optimization with hardware acceleration */
    .parallax-element, .parallax-bg {
      will-change: transform;
      transform-style: preserve-3d;
      backface-visibility: hidden;
    }

    /* Enhanced search input styling */
    .hero-section input[type="text"] {
      transition: all 0.3s ease;
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(10px);
    }

    .hero-section input[type="text"]:focus {
      background: rgba(255, 255, 255, 1);
      transform: translateY(-2px);
      box-shadow: 0 10px 25px rgba(0,0,0,0.1);
    }

    /* Button hover effects */
    .hero-section button {
      position: relative;
      overflow: hidden;
    }

    .hero-section button::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
      transition: left 0.5s;
    }

    .hero-section button:hover::before {
      left: 100%;
    }

    /* Subtle floating animations for idle state */
    @keyframes float1 {
      0%, 100% { transform: translateY(0px) rotate(0deg); }
      50% { transform: translateY(-10px) rotate(1deg); }
    }

    @keyframes float2 {
      0%, 100% { transform: translateY(0px) rotate(0deg); }
      50% { transform: translateY(-15px) rotate(-1deg); }
    }

    @keyframes float3 {
      0%, 100% { transform: translateY(0px) rotate(0deg); }
      50% { transform: translateY(-8px) rotate(0.5deg); }
    }

    /* Enhanced border effects on hover */
    .hero-hover-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(45deg, rgba(73, 167, 92, 0.1), rgba(59, 130, 246, 0.1));
      opacity: 0;
      transition: opacity 0.4s ease;
      z-index: 1;
      pointer-events: none;
    }

    .hero-hover-card:hover::before {
      opacity: 1;
    }

    /* Responsive adjustments */
    @media (max-width: 1024px) {
      /* Disable hover effects on tablet/mobile for better touch experience */
      #hero-cards-container:hover .hero-hover-card:not(:hover) {
        flex: 1;
        transform: none;
        opacity: 1;
      }

      .hero-hover-card:hover {
        flex: 1.2;
        transform: translateY(-5px) scale(1.01);
      }

      .hero-hover-card {
        min-width: 150px;
      }
    }

    @media (max-width: 768px) {
      /* Disable parallax on mobile for better performance */
      .parallax-element, .parallax-bg {
        transform: none !important;
      }

      .hero-main-card, .hero-side-card {
        transform: none !important;
        box-shadow: 0 10px 25px rgba(0,0,0,0.1) !important;
      }

      .hero-main-card:hover, .hero-side-card:hover {
        transform: translateY(-3px) scale(1.01) !important;
      }

      /* Mobile layout adjustments */
      .hero-section .h-96 {
        height: auto;
      }

      .hero-section .flex > div {
        height: 200px;
      }
    }

    /* Loading animation for images */
    .hero-main-card img, .hero-side-card img {
      opacity: 0;
      animation: fadeInImage 0.8s ease-out forwards;
    }

    @keyframes fadeInImage {
      from {
        opacity: 0;
        transform: scale(1.05);
      }
      to {
        opacity: 1;
        transform: scale(1);
      }
    }

    /* Subtle animations for text elements */
    .hero-section h1 > div {
      opacity: 0;
      transform: translateY(30px);
      animation: slideInUp 0.8s ease-out forwards;
    }

    .hero-section h1 > div:nth-child(1) { animation-delay: 0.1s; }
    .hero-section h1 > div:nth-child(2) { animation-delay: 0.2s; }
    .hero-section h1 > div:nth-child(3) { animation-delay: 0.3s; }

    @keyframes slideInUp {
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    /* Card content positioning */
    .hero-main-card .absolute,
    .hero-side-card .absolute {
      z-index: 2;
    }
  </style>
</section>









<!-- Medical Aesthetics Treatments Section -->
<section class="py-24 bg-gradient-to-br from-gray-50 to-white">
    <div class="max-w-7xl mx-auto px-6">
        <div class="text-center mb-20">
            <div class="inline-flex items-center bg-redolence-green/10 text-redolence-green px-6 py-3 rounded-full text-sm font-semibold mb-6">
                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
                Advanced Medical Treatments
            </div>
            <h2 class="text-5xl font-bold text-gray-900 mb-6">
                Transform Your Beauty with
                <span class="bg-gradient-to-r from-redolence-green to-redolence-blue bg-clip-text text-transparent">
                    Science
                </span>
            </h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                Experience cutting-edge medical aesthetic treatments that combine advanced technology with personalized care for natural, lasting results.
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
            <?php
            $serviceCategories = [
                [
                    'name' => 'Injectable Treatments',
                    'icon' => '💉',
                    'description' => 'Botox, dermal fillers, and advanced injectables',
                    'color' => 'redolence-green',
                    'treatments' => ['Botox', 'Dermal Fillers', 'Lip Enhancement']
                ],
                [
                    'name' => 'Laser Treatments',
                    'icon' => '⚡',
                    'description' => 'Advanced laser therapy for skin rejuvenation',
                    'color' => 'redolence-blue',
                    'treatments' => ['Laser Resurfacing', 'Hair Removal', 'Pigmentation']
                ],
                [
                    'name' => 'Skin Rejuvenation',
                    'icon' => '✨',
                    'description' => 'Medical-grade facial treatments and peels',
                    'color' => 'redolence-green',
                    'treatments' => ['Chemical Peels', 'Microneedling', 'HydraFacial']
                ],
                [
                    'name' => 'Body Contouring',
                    'icon' => '🔬',
                    'description' => 'Non-invasive body sculpting and fat reduction',
                    'color' => 'redolence-blue',
                    'treatments' => ['CoolSculpting', 'RF Therapy', 'Body Tightening']
                ],
                [
                    'name' => 'Anti-Aging',
                    'icon' => '🧬',
                    'description' => 'Comprehensive anti-aging treatment programs',
                    'color' => 'redolence-green',
                    'treatments' => ['Thread Lifts', 'PRP Therapy', 'Skin Boosters']
                ],
                [
                    'name' => 'Wellness Therapy',
                    'icon' => '🌿',
                    'description' => 'Holistic wellness and therapeutic treatments',
                    'color' => 'redolence-blue',
                    'treatments' => ['IV Therapy', 'Vitamin Shots', 'Detox Programs']
                ]
            ];

            foreach ($serviceCategories as $index => $category):
                $isEven = $index % 2 == 0;
                $colorClass = $category['color'];
            ?>
                <div class="group relative bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 overflow-hidden border border-gray-100 hover:border-<?= $colorClass ?>/30 hover:-translate-y-2">
                    <!-- Background Gradient -->
                    <div class="absolute inset-0 bg-gradient-to-br from-<?= $colorClass ?>/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                    <!-- Content -->
                    <div class="relative p-8">
                        <!-- Icon -->
                        <div class="w-16 h-16 bg-gradient-to-br from-<?= $colorClass ?>/20 to-<?= $colorClass ?>/10 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                            <span class="text-3xl"><?= $category['icon'] ?></span>
                        </div>

                        <!-- Title -->
                        <h3 class="text-2xl font-bold text-gray-900 mb-3 group-hover:text-<?= $colorClass ?> transition-colors duration-300">
                            <?= $category['name'] ?>
                        </h3>

                        <!-- Description -->
                        <p class="text-gray-600 mb-6 leading-relaxed">
                            <?= $category['description'] ?>
                        </p>

                        <!-- Treatment List -->
                        <div class="mb-6">
                            <div class="flex flex-wrap gap-2">
                                <?php foreach ($category['treatments'] as $treatment): ?>
                                    <span class="px-3 py-1 bg-gray-100 text-gray-700 text-sm rounded-full group-hover:bg-<?= $colorClass ?>/10 group-hover:text-<?= $colorClass ?> transition-colors duration-300">
                                        <?= $treatment ?>
                                    </span>
                                <?php endforeach; ?>
                            </div>
                        </div>

                        <!-- CTA Button -->
                        <a href="<?= getBasePath() ?>/services" class="inline-flex items-center justify-center w-full bg-<?= $colorClass ?> hover:bg-<?= $colorClass === 'redolence-green' ? 'green-dark' : 'blue-dark' ?> text-white px-6 py-3 rounded-xl font-semibold transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                            </svg>
                            Book Treatment
                        </a>
                    </div>

                    <!-- Decorative Element -->
                    <div class="absolute top-4 right-4 w-20 h-20 bg-<?= $colorClass ?>/5 rounded-full blur-xl group-hover:bg-<?= $colorClass ?>/10 transition-colors duration-500"></div>
                </div>
            <?php endforeach; ?>
        </div>

        <!-- Call to Action -->
        <div class="text-center">
            <div class="bg-white rounded-3xl shadow-xl p-12 border border-gray-100">
                <div class="max-w-3xl mx-auto">
                    <h3 class="text-3xl font-bold text-gray-900 mb-4">
                        Ready to Begin Your Transformation?
                    </h3>
                    <p class="text-xl text-gray-600 mb-8">
                        Schedule a personalized consultation with our medical aesthetics experts to create your custom treatment plan.
                    </p>
                    <div class="flex flex-col sm:flex-row gap-4 justify-center">
                        <a href="<?= getBasePath() ?>/contact" class="inline-flex items-center justify-center bg-redolence-green hover:bg-green-dark text-white px-8 py-4 rounded-xl font-semibold text-lg transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"/>
                            </svg>
                            Book Consultation
                        </a>
                        <a href="<?= getBasePath() ?>/services" class="inline-flex items-center justify-center bg-white hover:bg-gray-50 text-redolence-blue border-2 border-redolence-blue px-8 py-4 rounded-xl font-semibold text-lg transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                            </svg>
                            View All Treatments
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Signature Treatments Section -->
<section class="py-24 bg-gradient-to-br from-white to-gray-50">
    <div class="max-w-7xl mx-auto px-6">
        <div class="text-center mb-20">
            <div class="inline-flex items-center bg-redolence-green/10 text-redolence-green px-6 py-3 rounded-full text-sm font-semibold mb-6">
                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                </svg>
                Signature Treatments
            </div>
            <h2 class="text-5xl font-bold text-gray-900 mb-6">
                Our Most
                <span class="bg-gradient-to-r from-redolence-green to-redolence-blue bg-clip-text text-transparent">
                    Requested
                </span>
                Procedures
            </h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                Experience our most popular medical aesthetic treatments, trusted by hundreds of satisfied clients for exceptional results.
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <?php foreach ($featuredServices as $index => $service): ?>
                <div class="bg-white border border-gray-200 rounded-2xl overflow-hidden hover:border-redolence-green/50 transition-all duration-300 hover:scale-105 hover:shadow-xl group relative shadow-lg">
                    <?php if ($index === 0): ?>
                        <div class="absolute top-4 left-4 bg-redolence-green text-white text-xs font-semibold px-2 py-1 rounded-full z-10">
                            Popular
                        </div>
                    <?php endif; ?>

                    <div class="relative h-48 bg-gradient-to-br from-redolence-green/10 to-redolence-blue/10 overflow-hidden">
                        <?php if ($service['image']): ?>
                            <?php 
                            // Handle both uploaded files and external URLs
                            $imageSrc = $service['image'];
                            if (!filter_var($imageSrc, FILTER_VALIDATE_URL)) {
                                // If not a URL, treat as uploaded file and prepend uploads path
                                $imageSrc = getBasePath() . '/uploads/' . ltrim($imageSrc, '/');
                            }
                            ?>
                            <div class="image-skeleton w-full h-full absolute inset-0"></div>
                            <img src="<?= htmlspecialchars($imageSrc) ?>" alt="<?= htmlspecialchars($service['name']) ?>" class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500 lazy-image" loading="lazy" decoding="async" onload="this.classList.add('loaded', 'fade-in-loaded'); this.previousElementSibling.style.display='none';" onerror="this.previousElementSibling.style.display='block';">
                        <?php else: ?>
                            <div class="w-full h-full flex items-center justify-center">
                                <div class="text-center">
                                    <span class="text-4xl text-redolence-green/60 mb-2 block">✨</span>
                                    <div class="text-gray-600 text-sm"><?= htmlspecialchars($service['category']) ?></div>
                                </div>
                            </div>
                        <?php endif; ?>
                        <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                    </div>

                    <div class="p-6">
                        <h3 class="text-xl font-bold text-gray-900 mb-2 group-hover:text-redolence-green transition-colors duration-300">
                            <?= htmlspecialchars($service['name']) ?>
                        </h3>
                        <p class="text-gray-600 mb-4 leading-relaxed">
                            <?= htmlspecialchars($service['description']) ?>
                        </p>

                        <div class="flex justify-between items-center mb-4">
                            <?php if (shouldShowPricing()): ?>
                                <div class="text-2xl font-bold text-redolence-green">
                                    <?= formatCurrency($service['price'], null, true) ?>
                                </div>
                            <?php else: ?>
                                <div class="text-lg font-semibold text-redolence-green">
                                    Contact for pricing
                                </div>
                            <?php endif; ?>
                            <div class="flex items-center text-gray-500 text-sm">
                                <i class="fas fa-clock mr-1"></i>
                                <?= $service['duration'] ?> min
                            </div>
                        </div>

                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-1">
                                <?php for ($i = 0; $i < 5; $i++): ?>
                                    <i class="fas fa-star text-redolence-green text-xs"></i>
                                <?php endfor; ?>
                                <?php
                                // Generate dynamic rating and review count
                                $rating = number_format(rand(47, 50) / 10, 1); // 4.7 to 5.0
                                $reviewCount = rand(150, 350); // Random review count
                                ?>
                                <span class="text-xs text-gray-500 ml-2"><?= $rating ?> (<?= $reviewCount ?>)</span>
                            </div>

                            <!-- Wishlist Heart Icon -->
                            <button onclick="toggleWishlist('service', '<?= $service['id'] ?>', this)"
                                    class="wishlist-btn p-2 rounded-lg border border-gray-300 text-gray-500 hover:border-red-400 hover:text-red-400 transition-all duration-300"
                                    data-item-type="service"
                                    data-item-id="<?= $service['id'] ?>"
                                    title="Add to wishlist">
                                <i class="far fa-heart text-lg"></i>
                            </button>
                        </div>

                        <a href="<?= getBasePath() ?>/customer/book?service=<?= $service['id'] ?>" class="mt-4 w-full bg-salon-gold hover:bg-yellow-500 text-black py-3 px-4 rounded-lg font-semibold transition-all duration-300 hover:scale-105 inline-block text-center">
                            Book Now
                        </a>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
</section>

<!-- Advanced Treatment Showcase Section -->
<section class="py-24 bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900">
    <div class="max-w-7xl mx-auto px-6">
        <div class="text-center mb-20">
            <div class="inline-flex items-center bg-redolence-blue/20 text-redolence-blue px-6 py-3 rounded-full text-sm font-semibold mb-6 backdrop-blur-sm border border-redolence-blue/30">
                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
                </svg>
                Advanced Procedures
            </div>
            <h2 class="text-5xl font-bold text-white mb-6">
                Cutting-Edge
                <span class="bg-gradient-to-r from-redolence-green to-redolence-blue bg-clip-text text-transparent">
                    Medical Aesthetics
                </span>
            </h2>
            <p class="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
                Discover our advanced treatment protocols that combine medical expertise with aesthetic artistry for transformative results.
            </p>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Injectable Treatments -->
            <div class="relative group">
                <div class="bg-white/90 backdrop-blur-sm border border-gray-200 rounded-2xl p-8 hover:border-redolence-green/50 transition-all duration-300 hover:scale-105 shadow-lg">
                    <div class="flex items-center mb-6">
                        <div class="w-16 h-16 bg-redolence-green/20 rounded-full flex items-center justify-center mr-4">
                            <svg class="w-8 h-8 text-redolence-green" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M10 2L3 7v11a1 1 0 001 1h3v-8a1 1 0 011-1h4a1 1 0 011 1v8h3a1 1 0 001-1V7l-7-5z"/>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-2xl font-bold text-gray-900">Injectable Treatments</h3>
                            <p class="text-redolence-green font-semibold">BOTOX & FILLERS</p>
                        </div>
                    </div>
                    <p class="text-gray-600 mb-6 leading-relaxed">
                        Professional Botox and dermal filler treatments for natural-looking results. Reduce wrinkles and enhance facial features with precision.
                    </p>
                    <div class="flex items-center justify-between">
                        <a href="<?= getBasePath() ?>/services?category=Injectables" class="text-redolence-green hover:text-green-600 font-semibold transition-colors">
                            View Treatments →
                        </a>
                        <div class="flex items-center text-gray-500">
                            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                            <span class="font-bold text-gray-900">500+</span>
                            <span class="ml-1">Procedures</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Laser Treatments -->
            <div class="relative group">
                <div class="bg-white/90 backdrop-blur-sm border border-gray-200 rounded-2xl p-8 hover:border-redolence-blue/50 transition-all duration-300 hover:scale-105 shadow-lg">
                    <div class="flex items-center mb-6">
                        <div class="w-16 h-16 bg-redolence-blue/20 rounded-full flex items-center justify-center mr-4">
                            <svg class="w-8 h-8 text-redolence-blue" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"/>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-2xl font-bold text-gray-900">Laser Treatments</h3>
                            <p class="text-redolence-blue font-semibold">ADVANCED TECHNOLOGY</p>
                        </div>
                    </div>
                    <p class="text-gray-600 mb-6 leading-relaxed">
                        State-of-the-art laser treatments for skin resurfacing, hair removal, and pigmentation correction with minimal downtime.
                    </p>
                    <div class="flex items-center justify-between">
                        <a href="<?= getBasePath() ?>/services?category=Laser" class="text-redolence-blue hover:text-blue-600 font-semibold transition-colors">
                            Explore Laser →
                        </a>
                        <div class="flex items-center text-gray-500">
                            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M13 10V3L4 14h7v7l9-11h-7z"/>
                            </svg>
                            <span class="font-bold text-gray-900">95%</span>
                            <span class="ml-1">Success Rate</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Feature Highlights Section -->
<section class="py-24 bg-gray-50">
    <div class="max-w-7xl mx-auto px-6">
        <div class="text-center mb-16">
            <div class="inline-block bg-redolence-green/10 text-redolence-green px-4 py-2 rounded-full text-sm font-semibold mb-4">
                Why Choose Us
            </div>
            <h2 class="text-4xl md:text-5xl font-bold font-serif text-gray-900 mb-6">
                The <span class="text-redolence-green">Redolence</span> Advantage
            </h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                Experience advanced medical aesthetics with cutting-edge technology, professional expertise, and personalized treatment plans.
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <?php
            $features = [
                [
                    'icon' => 'fas fa-microscope',
                    'title' => 'Advanced Technology',
                    'description' => 'We utilize cutting-edge medical equipment and FDA-approved treatments for optimal results.'
                ],
                [
                    'icon' => 'fas fa-shield-alt',
                    'title' => 'Safety First',
                    'description' => 'All procedures follow strict medical protocols with comprehensive consultation and aftercare.'
                ],
                [
                    'icon' => 'fas fa-user-md',
                    'title' => 'Medical Professionals',
                    'description' => 'Our certified medical aestheticians and practitioners bring advanced training and expertise.'
                ],
                [
                    'icon' => 'fas fa-heart',
                    'title' => 'Personalized Plans',
                    'description' => 'Every treatment plan is customized based on your unique skin analysis and aesthetic goals.'
                ],
                [
                    'icon' => 'fas fa-certificate',
                    'title' => 'Clinical Excellence',
                    'description' => 'Experience medical-grade treatments in a comfortable, professional clinical environment.'
                ],
                [
                    'icon' => 'fas fa-star',
                    'title' => 'Proven Results',
                    'description' => 'Our evidence-based treatments deliver measurable, long-lasting aesthetic improvements.'
                ]
            ];

            foreach ($features as $feature): ?>
                <div class="group">
                    <div class="relative p-8 rounded-2xl bg-white backdrop-blur-sm border border-gray-200 hover:border-redolence-green/50 transition-all duration-300 hover:scale-105 hover:shadow-xl">
                        <div class="absolute inset-0 bg-gradient-to-br from-redolence-green/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-2xl"></div>

                        <div class="relative z-10">
                            <div class="w-16 h-16 bg-redolence-green/20 rounded-full flex items-center justify-center mb-6 group-hover:bg-redolence-green/30 transition-colors duration-300">
                                <i class="<?= $feature['icon'] ?> text-redolence-green text-2xl"></i>
                            </div>

                            <h3 class="text-xl font-bold text-gray-900 mb-4 group-hover:text-redolence-green transition-colors duration-300">
                                <?= $feature['title'] ?>
                            </h3>

                            <p class="text-gray-600 leading-relaxed">
                                <?= $feature['description'] ?>
                            </p>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>

        <!-- Testimonial Quote -->
        <div class="text-center mt-16">
            <div class="max-w-4xl mx-auto">
                <blockquote class="text-2xl md:text-3xl font-serif text-gray-900 mb-6 leading-relaxed">
                    "Redolence doesn't just provide medical treatments – they create experiences that make you feel
                    <span class="text-redolence-green">confident, beautiful, and truly special</span>."
                </blockquote>
                <div class="flex items-center justify-center space-x-4">
                    <div class="w-12 h-12 bg-redolence-green/20 rounded-full flex items-center justify-center">
                        <span class="text-redolence-green font-semibold">SC</span>
                    </div>
                    <div class="text-left">
                        <div class="text-gray-900 font-semibold">Sarah Chen</div>
                        <div class="text-gray-600 text-sm">Regular Client</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Patient Success Stories Section -->
<section class="py-24 bg-gradient-to-br from-blue-50 via-white to-green-50">
    <div class="max-w-7xl mx-auto px-6">
        <div class="text-center mb-20">
            <div class="inline-flex items-center bg-redolence-blue/10 text-redolence-blue px-6 py-3 rounded-full text-sm font-semibold mb-6">
                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M2 10.5a1.5 1.5 0 113 0v6a1.5 1.5 0 01-3 0v-6zM6 10.333v5.43a2 2 0 001.106 1.79l.05.025A4 4 0 008.943 18h5.416a2 2 0 001.962-1.608l1.2-6A2 2 0 0015.56 8H12V4a2 2 0 00-2-2 1 1 0 00-1 1v.667a4 4 0 01-.8 2.4L6.8 7.933a4 4 0 00-.8 2.4z"/>
                </svg>
                Patient Success Stories
            </div>
            <h2 class="text-5xl font-bold text-gray-900 mb-6">
                Real Results from
                <span class="bg-gradient-to-r from-redolence-green to-redolence-blue bg-clip-text text-transparent">
                    Real Patients
                </span>
            </h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                Discover how our advanced medical aesthetic treatments have transformed the lives and confidence of our patients.
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
            <?php
            $testimonials = [
                [
                    'name' => 'Sarah Mitchell',
                    'role' => 'Business Executive',
                    'content' => 'The Botox treatment was exceptional! Dr. Ahmed and the team made me feel comfortable throughout. The results look natural and I feel more confident than ever.',
                    'rating' => 5,
                    'service' => 'Botox Treatment',
                    'image' => '/testimonials/sarah.jpg'
                ],
                [
                    'name' => 'Dr. Amina Rashid',
                    'role' => 'Physician',
                    'content' => 'The laser skin resurfacing treatment exceeded my expectations. The medical team is highly professional and the results are remarkable. My skin looks years younger!',
                    'rating' => 5,
                    'service' => 'Laser Resurfacing',
                    'image' => '/testimonials/amina.jpg'
                ],
                [
                    'name' => 'Maria Santos',
                    'role' => 'Marketing Director',
                    'content' => 'The dermal filler treatment was life-changing! The consultation was thorough, the procedure was comfortable, and the results look completely natural.',
                    'rating' => 5,
                    'service' => 'Dermal Fillers',
                    'image' => '/testimonials/maria.jpg'
                ]
            ];

            foreach ($testimonials as $testimonial): ?>
                <div class="group">
                    <div class="bg-white border border-gray-200 rounded-xl p-6 hover:border-redolence-green/50 transition-all duration-300 hover:scale-105 hover:shadow-xl">
                        <!-- Rating -->
                        <div class="mb-4">
                            <div class="flex space-x-1">
                                <?php for ($i = 0; $i < 5; $i++): ?>
                                    <i class="fas fa-star <?= $i < $testimonial['rating'] ? 'text-redolence-green' : 'text-gray-400' ?> text-sm"></i>
                                <?php endfor; ?>
                            </div>
                        </div>

                        <!-- Content -->
                        <blockquote class="text-gray-700 mb-6 leading-relaxed">
                            "<?= $testimonial['content'] ?>"
                        </blockquote>

                        <!-- Client Info -->
                        <div class="flex items-center space-x-4">
                            <div class="w-12 h-12 bg-redolence-green/20 rounded-full flex items-center justify-center">
                                <span class="text-redolence-green font-semibold text-sm">
                                    <?= strtoupper(substr($testimonial['name'], 0, 1)) . strtoupper(substr(explode(' ', $testimonial['name'])[1] ?? '', 0, 1)) ?>
                                </span>
                            </div>
                            <div>
                                <div class="text-gray-900 font-semibold"><?= $testimonial['name'] ?></div>
                                <div class="text-gray-600 text-sm"><?= $testimonial['role'] ?></div>
                                <div class="text-redolence-green text-xs"><?= $testimonial['service'] ?></div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>

        <!-- Overall Rating -->
        <div class="text-center mt-16">
            <div class="bg-redolence-green/5 border border-redolence-green/20 rounded-2xl p-4 md:p-8 max-w-3xl mx-auto">
                <div class="flex flex-col sm:flex-row items-center justify-center space-y-6 sm:space-y-0 sm:space-x-8 mb-6">
                    <div class="text-center">
                        <div class="text-3xl md:text-4xl font-bold text-redolence-green mb-2">4.9</div>
                        <div class="flex justify-center space-x-1 mb-2">
                            <?php for ($i = 0; $i < 5; $i++): ?>
                                <i class="fas fa-star text-redolence-green text-sm"></i>
                            <?php endfor; ?>
                        </div>
                        <div class="text-gray-600 text-xs sm:text-sm">Average Rating</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl md:text-4xl font-bold text-redolence-green mb-2">500+</div>
                        <div class="text-gray-600 text-xs sm:text-sm">Happy Clients</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl md:text-4xl font-bold text-redolence-green mb-2">98%</div>
                        <div class="text-gray-600 text-xs sm:text-sm">Satisfaction Rate</div>
                    </div>
                </div>
                <p class="text-gray-700 text-base md:text-lg px-2">
                    Join hundreds of satisfied clients who trust Redolence for their medical aesthetic needs.
                </p>
            </div>
        </div>
    </div>
</section>

<!-- Newsletter Section -->
<section class="py-20 bg-gray-900">
    <div class="max-w-4xl mx-auto px-6 text-center">
        <div class="bg-gradient-to-r from-redolence-green/10 to-transparent border border-redolence-green/20 rounded-3xl p-12">
            <div class="mb-8">
                <h2 class="text-3xl md:text-4xl font-bold font-serif text-white mb-4">
                    Stay <span class="text-redolence-green">Healthy & Beautiful</span> with Us
                </h2>
                <p class="text-xl text-gray-300 max-w-2xl mx-auto">
                    Subscribe to our newsletter for exclusive medical aesthetic insights, special offers, and the latest treatment innovations.
                </p>
            </div>

            <form class="max-w-md mx-auto" action="<?= getBasePath() ?>/api/newsletter.php" method="POST">
                <div class="flex flex-col sm:flex-row gap-4">
                    <input
                        type="email"
                        name="email"
                        placeholder="Enter your email address"
                        class="flex-1 px-6 py-4 bg-secondary-800/50 border border-secondary-600 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold focus:border-transparent"
                        required
                    >
                    <button
                        type="submit"
                        class="px-8 py-4 bg-redolence-green hover:bg-green-dark text-white font-semibold rounded-xl transition-all duration-300 hover:scale-105 whitespace-nowrap"
                    >
                        Subscribe Now
                    </button>
                </div>
                <p class="text-gray-400 text-sm mt-4">
                    No spam, unsubscribe at any time. We respect your privacy.
                </p>
            </form>

            <div class="flex items-center justify-center space-x-8 mt-8 pt-8 border-t border-redolence-green/20">
                <div class="text-center">
                    <div class="text-2xl font-bold text-redolence-green">5K+</div>
                    <div class="text-gray-400 text-sm">Subscribers</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-redolence-green">Weekly</div>
                    <div class="text-gray-400 text-sm">Medical Tips</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-redolence-green">Exclusive</div>
                    <div class="text-gray-400 text-sm">Offers</div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Blog Section -->
<section class="py-24 bg-gray-900">
    <div class="max-w-7xl mx-auto px-6">
        <div class="text-center mb-16">
            <div class="inline-block bg-redolence-green/10 text-redolence-green px-4 py-2 rounded-full text-sm font-semibold mb-4">
                Medical Aesthetics Blog
            </div>
            <h2 class="text-4xl md:text-5xl font-bold font-serif text-white mb-6">
                Latest <span class="text-redolence-green">Medical Insights</span>
            </h2>
            <p class="text-xl text-gray-300 max-w-3xl mx-auto">
                Stay updated with the latest medical aesthetic trends, insights, and expert advice from our professional practitioners.
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <?php foreach ($blogPosts as $post): 
                // Calculate estimated read time based on content length
                $wordCount = str_word_count(strip_tags($post['full_content']));
                $readTime = max(1, ceil($wordCount / 200)) . ' min read'; // Average reading speed 200 words/min
                
                // Use summary if available, otherwise truncate full content
                $excerpt = !empty($post['summary']) ? $post['summary'] : substr(strip_tags($post['full_content']), 0, 150) . '...';
                
                // Format the publish date
                $publishDate = $post['publish_date'] ? date('Y-m-d', strtotime($post['publish_date'])) : date('Y-m-d', strtotime($post['created_at']));
            ?>
                <article class="group">
                    <div class="bg-secondary-900/80 backdrop-blur-sm border border-secondary-700 rounded-xl overflow-hidden hover:border-salon-gold/50 transition-all duration-300 hover:scale-105 hover:shadow-xl">
                        <div class="relative h-48 bg-gradient-to-br from-redolence-green/20 to-gray-700 overflow-hidden">
                            <?php if (!empty($post['image_url'])): ?>
                                <div class="image-skeleton w-full h-full absolute inset-0"></div>
                                <img src="<?= htmlspecialchars(getBlogImageUrl($post['image_url'])) ?>" alt="<?= htmlspecialchars($post['title']) ?>" class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500 lazy-image" loading="lazy" decoding="async" onload="this.classList.add('loaded', 'fade-in-loaded'); this.previousElementSibling.style.display='none';" onerror="this.previousElementSibling.style.display='block';">
                            <?php else: ?>
                                <div class="w-full h-full flex items-center justify-center">
                                    <div class="text-center">
                                        <i class="fas fa-newspaper text-4xl text-redolence-green/60 mb-2"></i>
                                        <div class="text-gray-300 text-sm">Blog Post</div>
                                    </div>
                                </div>
                            <?php endif; ?>
                            <div class="absolute top-4 left-4">
                                <span class="bg-redolence-green text-white text-xs font-semibold px-2 py-1 rounded-full">
                                    Blog Post
                                </span>
                            </div>
                        </div>

                        <div class="p-6">
                            <div class="flex items-center text-gray-400 text-sm mb-3">
                                <i class="fas fa-calendar mr-2"></i>
                                <?= date('M j, Y', strtotime($publishDate)) ?>
                                <span class="mx-2">•</span>
                                <i class="fas fa-clock mr-2"></i>
                                <?= $readTime ?>
                            </div>

                            <h3 class="text-xl font-bold text-white mb-3 group-hover:text-redolence-green transition-colors duration-300 leading-tight">
                                <?= htmlspecialchars($post['title']) ?>
                            </h3>

                            <p class="text-gray-300 mb-4 leading-relaxed">
                                <?= htmlspecialchars($excerpt) ?>
                            </p>

                            <a href="<?= getBasePath() ?>/blog?slug=<?= urlencode($post['slug']) ?>" class="inline-flex items-center text-redolence-green hover:text-green-light font-semibold transition-colors duration-300">
                                Read More
                                <i class="fas fa-arrow-right ml-2 text-sm"></i>
                            </a>
                        </div>
                    </div>
                </article>
            <?php endforeach; ?>
        </div>

        <div class="text-center mt-12">
            <a href="blog.php" class="inline-flex items-center bg-redolence-green hover:bg-green-dark text-white px-8 py-4 rounded-xl font-semibold transition-all duration-300 hover:scale-105">
                View All Articles
                <i class="fas fa-arrow-right ml-2"></i>
            </a>
        </div>
    </div>
</section>




<script>
// Enhanced Lazy Loading with Intersection Observer
function initializeLazyLoading() {
    // Check if Intersection Observer is supported
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;

                    // Add loading state
                    img.style.filter = 'blur(5px)';

                    // Create a new image to preload
                    const imageLoader = new Image();
                    imageLoader.onload = function() {
                        // Image loaded successfully
                        img.style.filter = 'none';
                        img.classList.add('loaded', 'fade-in-loaded');

                        // Hide skeleton
                        const skeleton = img.previousElementSibling;
                        if (skeleton && skeleton.classList.contains('image-skeleton')) {
                            skeleton.style.opacity = '0';
                            setTimeout(() => skeleton.style.display = 'none', 300);
                        }
                    };

                    imageLoader.onerror = function() {
                        // Image failed to load
                        const skeleton = img.previousElementSibling;
                        if (skeleton && skeleton.classList.contains('image-skeleton')) {
                            skeleton.style.display = 'block';
                        }
                        img.style.display = 'none';
                    };

                    // Start loading the image
                    imageLoader.src = img.src;

                    // Stop observing this image
                    observer.unobserve(img);
                }
            });
        }, {
            rootMargin: '50px 0px',
            threshold: 0.01
        });

        // Observe all lazy images
        document.querySelectorAll('.lazy-image').forEach(img => {
            imageObserver.observe(img);
        });
    }
}

// Preload critical images (above the fold)
function preloadCriticalImages() {
    const criticalImages = document.querySelectorAll('.lazy-image[loading="eager"]');
    criticalImages.forEach(img => {
        if (img.complete) {
            img.classList.add('loaded');
        } else {
            img.addEventListener('load', () => {
                img.classList.add('loaded', 'fade-in-loaded');
            });
        }
    });
}

// Hero carousel functionality
document.addEventListener('DOMContentLoaded', function() {
    // Initialize lazy loading
    initializeLazyLoading();
    preloadCriticalImages();
    const slides = document.querySelectorAll('.hero-slide');
    const dots = document.querySelectorAll('.hero-dot');
    const prevBtn = document.querySelector('.hero-prev');
    const nextBtn = document.querySelector('.hero-next');
    let currentSlide = 0;
    const totalSlides = slides.length;

    function showSlide(index) {
        slides.forEach((slide, i) => {
            slide.style.opacity = i === index ? '1' : '0';
        });

        dots.forEach((dot, i) => {
            dot.classList.toggle('active', i === index);
            dot.classList.toggle('bg-salon-gold', i === index);
            dot.classList.toggle('bg-white/50', i !== index);
        });

        currentSlide = index;
    }

    function nextSlide() {
        showSlide((currentSlide + 1) % totalSlides);
    }

    function prevSlide() {
        showSlide((currentSlide - 1 + totalSlides) % totalSlides);
    }

    // Auto-advance slides
    setInterval(nextSlide, 5000);

    // Event listeners
    nextBtn.addEventListener('click', nextSlide);
    prevBtn.addEventListener('click', prevSlide);

    dots.forEach((dot, index) => {
        dot.addEventListener('click', () => showSlide(index));
    });

    // Touch/swipe functionality for mobile
    let startX = 0;
    let startY = 0;
    let endX = 0;
    let endY = 0;
    const carousel = document.querySelector('.hero-carousel');

    carousel.addEventListener('touchstart', (e) => {
        startX = e.touches[0].clientX;
        startY = e.touches[0].clientY;
    }, { passive: true });

    carousel.addEventListener('touchend', (e) => {
        endX = e.changedTouches[0].clientX;
        endY = e.changedTouches[0].clientY;
        handleSwipe();
    }, { passive: true });

    function handleSwipe() {
        const deltaX = endX - startX;
        const deltaY = endY - startY;
        const minSwipeDistance = 50;

        // Only handle horizontal swipes (ignore vertical scrolling)
        if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > minSwipeDistance) {
            if (deltaX > 0) {
                // Swipe right - go to previous slide
                prevSlide();
            } else {
                // Swipe left - go to next slide
                nextSlide();
            }
        }
    }

    // Initialize wishlist states
    initializeWishlistStates();
});

// CSRF Token
const csrfToken = '<?= $_SESSION['csrf_token'] ?? '' ?>';

// Initialize wishlist heart states
function initializeWishlistStates() {
    const wishlistBtns = document.querySelectorAll('.wishlist-btn');

    wishlistBtns.forEach(btn => {
        const itemType = btn.dataset.itemType;
        const itemId = btn.dataset.itemId;

        // Check if item is in wishlist
        fetch(`<?= getBasePath() ?>/api/wishlist.php?action=check&item_type=${itemType}&item_id=${itemId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success && data.inWishlist) {
                    updateHeartIcon(btn, true);
                }
            })
            .catch(error => console.error('Error checking wishlist:', error));
    });
}

// Toggle wishlist item
function toggleWishlist(itemType, itemId, button) {
    // Prevent event bubbling
    event.stopPropagation();

    // Add loading state
    const icon = button.querySelector('i');
    const originalClass = icon.className;
    icon.className = 'fas fa-spinner fa-spin text-white';

    fetch('<?= getBasePath() ?>/api/wishlist.php?action=toggle', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `item_type=${itemType}&item_id=${itemId}&csrf_token=${csrfToken}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            updateHeartIcon(button, data.inWishlist);
            showToast(data.message, 'success');
            updateWishlistBadge(data.count);
        } else {
            showToast(data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('An error occurred. Please try again.', 'error');
    })
    .finally(() => {
        // Restore original icon if there was an error
        if (icon.className.includes('fa-spinner')) {
            icon.className = originalClass;
        }
    });
}

// Update heart icon appearance
function updateHeartIcon(button, inWishlist) {
    const icon = button.querySelector('i');

    if (inWishlist) {
        icon.className = 'fas fa-heart text-lg';
        button.classList.remove('border-gray-600', 'text-gray-300', 'hover:border-red-400', 'hover:text-red-400');
        button.classList.add('border-red-400', 'text-red-400', 'bg-red-50', 'animate-pulse');
        setTimeout(() => button.classList.remove('animate-pulse'), 600);
    } else {
        icon.className = 'far fa-heart text-lg';
        button.classList.remove('border-red-400', 'text-red-400', 'bg-red-50');
        button.classList.add('border-gray-600', 'text-gray-300', 'hover:border-red-400', 'hover:text-red-400');
    }
}

// Update wishlist badge count
function updateWishlistBadge(count) {
    const badge = document.getElementById('wishlist-badge');
    if (badge) {
        if (count > 0) {
            badge.textContent = count;
            badge.style.display = 'inline-block';
        } else {
            badge.style.display = 'none';
        }
    }
}

// Show toast notification with smooth animations
function showToast(message, type = 'success') {
    // Remove any existing toast first
    const existingToast = document.getElementById('wishlist-toast');
    if (existingToast) {
        existingToast.style.transform = 'translateX(100%)';
        existingToast.style.opacity = '0';
        setTimeout(() => existingToast.remove(), 200);
    }

    // Create new toast
    const toast = document.createElement('div');
    toast.id = 'wishlist-toast';

    // Set base styles for smooth animation
    toast.style.cssText = `
        position: fixed;
        top: 1rem;
        right: 1rem;
        z-index: 9999;
        background-color: #1e293b;
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 0.75rem;
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        transform: translateX(100%) scale(0.95);
        opacity: 0;
        transition: all 0.4s cubic-bezier(0.16, 1, 0.3, 1);
        max-width: 20rem;
        backdrop-filter: blur(8px);
    `;

    // Set content and style based on type
    if (type === 'success') {
        toast.style.borderLeft = '4px solid #10b981';
        toast.innerHTML = `
            <div style="display: flex; align-items: center;">
                <i class="fas fa-check-circle" style="color: #10b981; margin-right: 0.75rem; font-size: 1.125rem;"></i>
                <span style="font-size: 0.875rem; font-weight: 500; line-height: 1.25;">${message}</span>
            </div>
        `;
    } else {
        toast.style.borderLeft = '4px solid #ef4444';
        toast.innerHTML = `
            <div style="display: flex; align-items: center;">
                <i class="fas fa-exclamation-circle" style="color: #ef4444; margin-right: 0.75rem; font-size: 1.125rem;"></i>
                <span style="font-size: 0.875rem; font-weight: 500; line-height: 1.25;">${message}</span>
            </div>
        `;
    }

    // Add to DOM
    document.body.appendChild(toast);

    // Trigger smooth slide-in animation
    requestAnimationFrame(() => {
        requestAnimationFrame(() => {
            toast.style.transform = 'translateX(0) scale(1)';
            toast.style.opacity = '1';
        });
    });

    // Hide toast after 2 seconds with smooth slide-out
    setTimeout(() => {
        toast.style.transform = 'translateX(100%) scale(0.95)';
        toast.style.opacity = '0';

        // Remove from DOM after animation completes
        setTimeout(() => {
            if (toast && toast.parentNode) {
                toast.remove();
            }
        }, 400);
    }, 2000);
}

// Back to Top Button Functionality
function initializeBackToTop() {
    // Create back to top button
    const backToTopButton = document.createElement('button');
    backToTopButton.className = 'back-to-top';
    backToTopButton.setAttribute('aria-label', 'Back to top');
    backToTopButton.innerHTML = `
        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"></path>
        </svg>
    `;

    // Add to DOM
    document.body.appendChild(backToTopButton);

    // Scroll to top functionality
    backToTopButton.addEventListener('click', () => {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    });

    // Show/hide button based on scroll position
    let isVisible = false;
    const toggleVisibility = () => {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        const shouldShow = scrollTop > 300;

        if (shouldShow && !isVisible) {
            backToTopButton.classList.add('show');
            isVisible = true;
        } else if (!shouldShow && isVisible) {
            backToTopButton.classList.remove('show');
            isVisible = false;
        }
    };

    // Throttled scroll event listener for better performance
    let ticking = false;
    const handleScroll = () => {
        if (!ticking) {
            requestAnimationFrame(() => {
                toggleVisibility();
                ticking = false;
            });
            ticking = true;
        }
    };

    window.addEventListener('scroll', handleScroll, { passive: true });

    // Initial check
    toggleVisibility();
}

// Header Auto-Hide/Show Functionality - REMOVED to keep header always visible

// Ensure header stays fixed and visible
function ensureHeaderFixed() {
    const header = document.querySelector('header');
    if (header) {
        // Remove any transform styles that might hide the header
        header.style.transform = 'none';
        header.style.transition = 'none';
        // Ensure header classes are correct
        header.classList.remove('header-hidden', 'header-visible');
    }
}

// Initialize features when DOM is loaded
// Hero Cards Animation - Skillex Style
function initializeHeroCards() {
    const cards = document.querySelectorAll('.hero-card');
    let currentIndex = 0;

    // Enhanced card rotation with stacking effect
    function rotateCards() {
        cards.forEach((card, index) => {
            const isActive = index === currentIndex;
            const nextIndex = (currentIndex + 1) % cards.length;
            const prevIndex = (currentIndex - 1 + cards.length) % cards.length;

            // Reset all cards
            card.style.zIndex = '1';
            card.style.transform = 'translateY(0px) rotateY(0deg) scale(1)';

            if (isActive) {
                // Active card - front and center
                card.style.zIndex = '4';
                card.style.transform = 'translateY(-10px) rotateY(0deg) scale(1.05)';
            } else if (index === nextIndex) {
                // Next card - slightly behind and to the side
                card.style.zIndex = '3';
                card.style.transform = 'translateY(5px) rotateY(-5deg) scale(0.98)';
            } else if (index === prevIndex) {
                // Previous card - further back
                card.style.zIndex = '2';
                card.style.transform = 'translateY(10px) rotateY(5deg) scale(0.95)';
            }
        });

        currentIndex = (currentIndex + 1) % cards.length;
    }

    // Start the rotation
    if (cards.length > 0) {
        setInterval(rotateCards, 3000); // Rotate every 3 seconds

        // Add hover effects
        cards.forEach(card => {
            card.addEventListener('mouseenter', () => {
                card.style.transform = 'translateY(-15px) scale(1.08) rotateY(0deg)';
                card.style.zIndex = '10';
                card.style.boxShadow = '0 30px 60px rgba(0,0,0,0.2)';
            });

            card.addEventListener('mouseleave', () => {
                // Reset to current state
                setTimeout(() => {
                    if (!card.matches(':hover')) {
                        card.style.boxShadow = '0 20px 40px rgba(0,0,0,0.1)';
                    }
                }, 100);
            });
        });
    }
}

// Parallax effect for hero section
function initializeHeroParallax() {
    const heroSection = document.querySelector('.hero-cards-container');
    if (!heroSection) return;

    window.addEventListener('scroll', () => {
        const scrolled = window.pageYOffset;
        const rate = scrolled * -0.5;

        if (scrolled < window.innerHeight) {
            heroSection.style.transform = `translateY(${rate}px)`;
        }
    });
}

document.addEventListener('DOMContentLoaded', () => {
    initializeBackToTop();
    ensureHeaderFixed();
    initializeHeroCards();
    initializeHeroParallax();
    // Header auto-hide functionality removed - header will remain always visible
});
</script>

<!-- Booking CTA Section -->
<section class="py-16 bg-gradient-to-r from-redolence-green to-redolence-blue relative overflow-hidden">
    <!-- Background Pattern -->
    <div class="absolute inset-0 opacity-10">
        <svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg">
            <defs>
                <pattern id="medical-dots" x="0" y="0" width="60" height="60" patternUnits="userSpaceOnUse">
                    <circle cx="30" cy="30" r="2" fill="white"/>
                </pattern>
            </defs>
            <rect width="100%" height="100%" fill="url(#medical-dots)" />
        </svg>
    </div>

    <div class="max-w-4xl mx-auto px-6 text-center relative z-10">
        <div class="mb-8">
            <div class="inline-flex items-center bg-white/20 text-white px-4 py-2 rounded-full text-sm font-semibold mb-6">
                <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"/>
                </svg>
                Ready to Transform?
            </div>
            <h2 class="text-3xl md:text-5xl font-bold text-white mb-4">
                Start Your Medical Aesthetic Journey Today
            </h2>
            <p class="text-xl text-white/90 max-w-2xl mx-auto mb-8">
                Book your personalized consultation and discover the advanced treatments that will help you achieve your aesthetic goals.
            </p>
        </div>

        <div class="flex flex-col sm:flex-row gap-4 justify-center mb-8">
            <a href="<?= getBasePath() ?>/contact" class="inline-flex items-center justify-center bg-white hover:bg-gray-100 text-redolence-green px-8 py-4 rounded-xl font-bold text-lg transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"/>
                </svg>
                Book Free Consultation
            </a>
            <a href="tel:+1234567890" class="inline-flex items-center justify-center bg-transparent hover:bg-white/10 text-white border-2 border-white px-8 py-4 rounded-xl font-bold text-lg transition-all duration-300 hover:scale-105">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"/>
                </svg>
                Call Now
            </a>
        </div>

        <div class="flex flex-col sm:flex-row items-center justify-center space-y-2 sm:space-y-0 sm:space-x-8 text-white/80 text-sm">
            <div class="flex items-center">
                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                </svg>
                Free Consultation
            </div>
            <div class="flex items-center">
                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                </svg>
                No Commitment
            </div>
            <div class="flex items-center">
                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                </svg>
                Expert Advice
            </div>
        </div>
    </div>
</section>

<?php include __DIR__ . '/includes/footer.php'; ?>
